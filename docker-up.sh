#!/bin/bash

# HERBIT Docker Management Script
# This script helps manage the HERBIT application using Docker Compose

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if Docker is running
check_docker() {
    if ! docker info >/dev/null 2>&1; then
        print_error "Docker is not running. Please start Docker first."
        exit 1
    fi
    print_success "Docker is running"
}

# Function to check if docker-compose is available
check_docker_compose() {
    if command -v docker-compose >/dev/null 2>&1; then
        DOCKER_COMPOSE_CMD="docker-compose"
    elif docker compose version >/dev/null 2>&1; then
        DOCKER_COMPOSE_CMD="docker compose"
    else
        print_error "Neither 'docker-compose' nor 'docker compose' is available"
        exit 1
    fi
    print_success "Using: $DOCKER_COMPOSE_CMD"
}

# Function to build the unified image
build_image() {
    print_status "Building HERBIT unified image..."
    if docker build -t herbit-unified:latest .; then
        print_success "Image built successfully"
    else
        print_error "Failed to build image"
        exit 1
    fi
}

# Function to start services
start_services() {
    print_status "Starting HERBIT services..."
    export TAG=latest
    
    if $DOCKER_COMPOSE_CMD up -d; then
        print_success "Services started successfully"
        
        # Wait a moment for services to initialize
        print_status "Waiting for services to initialize..."
        sleep 10
        
        # Show service status
        print_status "Service status:"
        $DOCKER_COMPOSE_CMD ps
        
        print_success "HERBIT is now running!"
        echo ""
        echo "🌐 Access URLs:"
        echo "   Frontend:     http://localhost:5173"
        echo "   Backend API:  http://localhost:8000"
        echo "   API Docs:     http://localhost:8000/docs"
        echo "   Database:     http://localhost:8080 (Adminer)"
        echo "   Auth Server:  http://localhost:5556"
        echo "   Tracing:      http://localhost:16686 (Jaeger)"
        echo ""
        echo "📋 Useful commands:"
        echo "   View logs:    ./docker-up.sh logs"
        echo "   Stop all:     ./docker-up.sh down"
        echo "   Restart:      ./docker-up.sh restart"
        
    else
        print_error "Failed to start services"
        exit 1
    fi
}

# Function to stop services
stop_services() {
    print_status "Stopping HERBIT services..."
    if $DOCKER_COMPOSE_CMD down; then
        print_success "Services stopped successfully"
    else
        print_error "Failed to stop services"
        exit 1
    fi
}

# Function to show logs
show_logs() {
    if [ -n "$2" ]; then
        print_status "Showing logs for service: $2"
        $DOCKER_COMPOSE_CMD logs -f "$2"
    else
        print_status "Showing logs for all services"
        $DOCKER_COMPOSE_CMD logs -f
    fi
}

# Function to restart services
restart_services() {
    if [ -n "$2" ]; then
        print_status "Restarting service: $2"
        $DOCKER_COMPOSE_CMD restart "$2"
    else
        print_status "Restarting all services"
        $DOCKER_COMPOSE_CMD restart
    fi
}

# Function to show service status
show_status() {
    print_status "Service status:"
    $DOCKER_COMPOSE_CMD ps
}

# Function to clean up everything
cleanup() {
    print_warning "This will remove all containers, volumes, and images. Are you sure? (y/N)"
    read -r response
    if [[ "$response" =~ ^([yY][eE][sS]|[yY])$ ]]; then
        print_status "Cleaning up..."
        $DOCKER_COMPOSE_CMD down -v --remove-orphans
        docker image rm herbit-unified:latest 2>/dev/null || true
        docker system prune -f
        print_success "Cleanup completed"
    else
        print_status "Cleanup cancelled"
    fi
}

# Function to show help
show_help() {
    echo "HERBIT Docker Management Script"
    echo ""
    echo "Usage: $0 [COMMAND] [OPTIONS]"
    echo ""
    echo "Commands:"
    echo "  up, start          Build and start all services"
    echo "  down, stop         Stop all services"
    echo "  restart [service]  Restart all services or specific service"
    echo "  logs [service]     Show logs for all services or specific service"
    echo "  ps, status         Show service status"
    echo "  build              Build the Docker image only"
    echo "  clean              Clean up all containers, volumes, and images"
    echo "  help               Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 up              # Start all services"
    echo "  $0 logs backend    # Show backend logs"
    echo "  $0 restart frontend # Restart frontend service"
    echo "  $0 down            # Stop all services"
}

# Main script logic
main() {
    # Check prerequisites
    check_docker
    check_docker_compose
    
    # Handle commands
    case "${1:-up}" in
        "up"|"start")
            build_image
            start_services
            ;;
        "down"|"stop")
            stop_services
            ;;
        "restart")
            restart_services "$@"
            ;;
        "logs")
            show_logs "$@"
            ;;
        "ps"|"status")
            show_status
            ;;
        "build")
            build_image
            ;;
        "clean")
            cleanup
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            print_error "Unknown command: $1"
            echo ""
            show_help
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@"
