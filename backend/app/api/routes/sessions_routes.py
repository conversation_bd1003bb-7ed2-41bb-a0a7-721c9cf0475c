"""
Sessions-related API routes for the quiz/assessment management system.
"""

import os
import random
from typing import Any, Dict, Optional

from fastapi import APIRouter, Depends, HTTPException, Query, Request, status
from pydantic import BaseModel

from ...api.middlewares.hashid_middleware import hash_ids_in_response
from ...models.assessment_manager import (
    calculate_total_score_for_assessment,
    get_performance_level_with_correct_total,
)
from ...models.assessment_manager import get_assessment_by_id
from ...models.sessions_manager import (
    create_session_in_db,
    find_or_create_user_by_details,
    generate_unique_session_code,
    get_completed_session_for_results,
    get_session_answers_for_results,
    get_session_for_start_or_validation,
    get_session_user_details,
    get_sessions_by_user_id,
    get_sessions_count,
    get_sessions_data,
    get_single_session_data,
    get_user_id_by_email,
    start_session_in_db,
    validate_assessment_exists,
    validate_session_code_format,
)
from ...utils.api_response import (
    error_response,
    paginated_response,
    raise_http_exception,
    success_response,
)
from ...utils.db_utils import safe_json_loads
from ...utils.hashid_utils import (
    decode_assessment_id,
    decode_session_code,
    detect_hash_type,
    encode_session_code,
)
from ...utils.logger import (
    error,
    info,
    warning,
)
from ...utils.rate_limiter import rate_limiter
from ...services.email_service import email_service

sessions_router = APIRouter()


def get_current_user(request: Request) -> dict:
    """Get current authenticated user from session."""
    user = request.session.get("user")
    if not user:
        raise HTTPException(status_code=401, detail="Authentication required. Please log in to access your sessions.")
    return user


class StartSessionRequest(BaseModel):
    session_code: str


class SessionCodeRequest(BaseModel):
    session_code: str


class SessionSubmissionRequest(BaseModel):
    session_code: str
    user_id: str


class Question(BaseModel):
    que_id: Any  # Can be int or str depending on your DB
    question: str
    options: Dict[str, str]


# =============================================================================
# GET SESSION API ENDPOINT AND HELPER FUNCTIONS
# =============================================================================


@sessions_router.get("/admin/sessions")
async def get_sessions(
    limit: int = Query(3, ge=1, le=100),
    offset: int = Query(0, ge=0),
    status_filter: Optional[str] = Query(
        None, description="Filter by session status: 'pending', 'completed', or 'all'"
    ),
):
    """
    Get sessions with pagination and optional status filtering

    Args:
        limit: Maximum number of items per page (default: 3)
        offset: Starting position (default: 0)
        status_filter: Filter by session status ('pending', 'completed', or 'all')
    """
    try:
        # Get total count
        total = get_sessions_count(status_filter)

        # Get paginated sessions data
        sessions = get_sessions_data(status_filter, limit, offset)

        # Transform response to include hashed IDs
        hashed_sessions = hash_ids_in_response(sessions)

        # Return paginated response
        return paginated_response(
            data=hashed_sessions,
            total=total,
            limit=limit,
            offset=offset,
            message="Sessions retrieved successfully",
            additional_data={"status_filter": status_filter or "all"},
        )
    except Exception as e:
        error(f"Error getting sessions: {str(e)}")
        return error_response(
            message=f"Error getting sessions: {str(e)}",
            code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error_type="InternalServerError",
        )


# =============================================================================
# GET SESSION DETAILS API ENDPOINT AND HELPER FUNCTIONS
# =============================================================================


def _enhance_completed_session_data(session_dict: dict) -> dict:
    """Add score and performance data for completed sessions."""
    if session_dict.get("status") == "completed" and session_dict.get("score") is not None:
        obtained_score = float(session_dict["score"])
        assessment_id = session_dict["assessment_id"]
        session_internal_id = session_dict["id"]

        # Calculate correct total possible score
        total_possible_score = calculate_total_score_for_assessment(assessment_id, session_internal_id)

        # Calculate performance level
        performance_level = get_performance_level_with_correct_total(obtained_score, assessment_id, session_internal_id)

        # Calculate percentage
        percentage = (obtained_score / total_possible_score * 100) if total_possible_score > 0 else 0

        # Add to session dict
        session_dict["obtained_score"] = obtained_score
        session_dict["total_possible_score"] = total_possible_score
        session_dict["percentage"] = round(percentage, 2)
        session_dict["performance_level"] = performance_level

    return session_dict


def _get_session_by_code(session_id: str) -> Optional[Dict]:
    """Get session by 6-digit session code by calling the data fetcher."""
    info(f"Using 6-digit session code: {session_id}")
    return get_single_session_data(session_code=session_id)


def _get_session_by_numeric_id(session_id: str) -> Optional[Dict]:
    """Get session by numeric ID by calling the data fetcher."""
    actual_id = int(session_id)
    info(f"Using numeric session ID: {actual_id}")
    return get_single_session_data(session_id=actual_id)


def _decode_and_get_session(session_id: str) -> Optional[Dict]:
    """Decode hash and get session using the central data fetching function."""
    info(f"Attempting to decode hash: {session_id}")
    hash_type = detect_hash_type(session_id)
    info(f"Detected hash type: {hash_type}")

    decoded_id = decode_session_code(session_id)
    if decoded_id:
        info(f"Successfully decoded as session hash: {session_id} -> {decoded_id}")
    elif hash_type == "assessment":
        decoded_id = decode_assessment_id(session_id)
        if decoded_id:
            info(f"Successfully decoded as assessment hash (treating as session): {session_id} -> {decoded_id}")

    info(f"Final decode result: {decoded_id}")

    if not decoded_id:
        warning(f"Invalid or undecodable session hash: {session_id}")
        raise HTTPException(status_code=400, detail=f"Invalid session hash: {session_id}")

    info(f"Successfully decoded {session_id} to ID: {decoded_id}")
    session_dict = get_single_session_data(session_id=decoded_id)

    if session_dict:
        # This post-processing is specific to the API response, so it stays here.
        for key, value in session_dict.items():
            if hasattr(value, "isoformat"):
                session_dict[key] = value.isoformat()
        return session_dict

    return None


def _generate_session_error_message(session_id: str) -> str:
    """Generate appropriate error message based on session_id format."""
    if len(session_id) == 6 and session_id.isdigit():
        return f"Session with 6-digit code {session_id} not found"
    elif session_id.isdigit():
        return f"Session with numeric ID {session_id} not found"
    else:
        return f"Session with ID {session_id} not found. Expected: numeric ID or 6-digit session code"


def _prepare_session_response(session_dict: dict) -> dict:
    """Prepare session dictionary for response."""
    if session_dict and "id_hash" not in session_dict:
        # Enhance completed session data
        session_dict = _enhance_completed_session_data(session_dict)

        # Convert datetime objects to ISO strings before hash transformation
        for key, value in session_dict.items():
            if hasattr(value, "isoformat"):
                session_dict[key] = value.isoformat()

        session_dict = hash_ids_in_response(session_dict)

    return session_dict


@sessions_router.get("/admin/sessions/{session_id}/details")
async def get_session_details_endpoint(session_id: str):
    """Get detailed session information by session ID (numeric ID or 6-digit session code)"""
    try:
        info(f"Session detail request for ID: {session_id} (type: {type(session_id)}, length: {len(session_id)})")

        session_dict = None

        # Check if it's a 6-digit session code first
        if len(session_id) == 6 and session_id.isdigit():
            session_dict = _get_session_by_code(session_id)
        # If it's a numeric ID (but not 6 digits), get directly by ID
        elif session_id.isdigit():
            session_dict = _get_session_by_numeric_id(session_id)
        else:
            # Try to decode hash
            session_dict = _decode_and_get_session(session_id)

        if not session_dict:
            error_detail = _generate_session_error_message(session_id)
            warning(f"Session lookup failed: {error_detail}")
            raise HTTPException(status_code=404, detail=error_detail)

        # Prepare session response
        session_dict = _prepare_session_response(session_dict)

        info(f"Returning session data keys: {list(session_dict.keys()) if session_dict else None}")

        return success_response(data=session_dict, message="Session details retrieved successfully")

    except HTTPException:
        raise
    except Exception as e:
        error(f"Error getting session details for ID {session_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error getting session details: {str(e)}")


# =============================================================================
# CREATE SESSION API ENDPOINT AND HELPER FUNCTIONS
# =============================================================================


def _process_user_identifier(identifier: str, default_email_domain: str) -> tuple[str, str, str]:
    """Process user identifier and return display_name, email, and external_id."""
    if "@" in identifier:
        email = identifier.lower()
        display_name = email.split("@")[0]
    else:
        display_name = identifier
        email = f"{display_name.lower()}@{default_email_domain}"

    external_id = display_name
    return display_name, email, external_id


def _validate_session_request(request_data: dict) -> tuple[int, list[str]]:
    """Validate session generation request and return assessment_id and user_identifiers."""
    assessment_id_str = request_data.get("assessment_id")
    usernames_str = request_data.get("usernames", "")

    if not assessment_id_str or not usernames_str:
        raise_http_exception(status_code=400, detail="Assessment ID and usernames are required.")

    try:
        assessment_id = int(assessment_id_str)
    except (ValueError, TypeError):
        raise_http_exception(status_code=400, detail="Assessment ID must be a valid number.")

    user_identifiers = [identifier.strip() for identifier in usernames_str.split(",") if identifier.strip()]

    if not user_identifiers:
        raise_http_exception(status_code=400, detail="No valid usernames provided.")

    return assessment_id, user_identifiers


def _send_quiz_invitation_email(
    session_data: dict,
    assessment_name: str,
    user_email: str,
    quiz_link: Optional[str] = None
) -> bool:
    """Send quiz invitation email to user"""
    if not email_service.is_configured:
        warning("Email service not configured, skipping email notification")
        return False
        
    if not user_email:
        warning(f"No email address for user {session_data['username']}, skipping email notification")
        return False
    
    try:
        success = email_service.send_quiz_invitation(
            to_email=user_email,
            user_name=session_data['username'],
            assessment_name=assessment_name,
            session_code=session_data['sessionCode'],
            quiz_link=quiz_link
        )
        
        if success:
            info(f"Quiz invitation email sent to {user_email} for session {session_data['sessionCode']}")
        else:
            warning(f"Failed to send quiz invitation email to {user_email}")
            
        return success
    except Exception as e:
        error(f"Error sending quiz invitation email to {user_email}: {str(e)}")
        return False


def _process_single_user_session(identifier: str, assessment_id: int, default_email_domain: str) -> dict:
    """Process a single user session creation."""
    # Process the identifier
    display_name, email, external_id = _process_user_identifier(identifier, default_email_domain)

    # Find or create the user in the database
    user_internal_id = find_or_create_user_by_details(
        display_name=display_name,
        email=email,
        external_id=external_id,
    )

    # Generate a unique session code
    session_code = generate_unique_session_code()

    # Insert the new session into the database
    session_db_id = create_session_in_db(session_code, user_internal_id, assessment_id)

    return {
        "id": session_db_id,
        "username": display_name,
        "sessionCode": session_code,
        "sessionDbId": session_db_id,
    }


@sessions_router.post("/admin/sessions")
async def generate_sessions(request_data: dict, _: None = Depends(rate_limiter)):
    """
    Generates assessment session codes for a list of users.
    It intelligently handles both usernames and email addresses as input.
    Also sends email invitations to users if email service is configured.
    """
    try:
        # Validate request
        assessment_id, user_identifiers = _validate_session_request(request_data)

        # Validate assessment exists and get assessment details
        assessment_details = validate_assessment_exists(assessment_id)
        assessment_name = assessment_details.get("name", "Assessment")

        created_sessions = []
        failed_sessions = []
        email_results = []
        default_email_domain = os.getenv("DEFAULT_EMAIL_DOMAIN", "example.com")

        # Process each user identifier
        for identifier in user_identifiers:
            try:
                session_data = _process_single_user_session(identifier, assessment_id, default_email_domain)
                created_sessions.append(session_data)

                # Attempt to send email invitation if email service is configured
                # Get email from the processed user identifier
                display_name, email, external_id = _process_user_identifier(identifier, default_email_domain)

                if email and email_service.is_configured:
                    # Check if email domain is "improwised.com" and skip sending
                    if email.lower().endswith("@improwised.com"):
                        info(f"Skipping email to {email} (improwised.com domain)")
                        email_results.append({
                            "user": identifier,
                            "email": email,
                            "sent": False,
                            "skipped": True,
                            "status": "Email skipped - improwised.com domain",
                            "reason": "improwised.com domain - email sending disabled"
                        })
                    else:
                        try:
                            # Construct quiz link (optional)
                            frontend_url = os.getenv("FRONTEND_URL")
                            quiz_link = f"{frontend_url}/quiz/{session_data['sessionCode']}"

                            email_sent = _send_quiz_invitation_email(
                                session_data=session_data,
                                assessment_name=assessment_name,
                                user_email=email,
                                quiz_link=quiz_link
                            )

                            if email_sent:
                                info(f"Email invitation sent successfully to {email}")
                                email_results.append({
                                    "user": identifier,
                                    "email": email,
                                    "sent": True,
                                    "status": "Email sent successfully"
                                })
                            else:
                                warning(f"Failed to send email to {email} - email service returned False")
                                email_results.append({
                                    "user": identifier,
                                    "email": email,
                                    "sent": False,
                                    "status": "Failed to send email - email service returned False"
                                })
                        except Exception as email_error:
                            warning(f"Failed to send email to {email}: {str(email_error)}")
                            email_results.append({
                                "user": identifier,
                                "email": email,
                                "sent": False,
                                "status": f"Email failed - {str(email_error)}",
                                "error": str(email_error)
                            })

            except Exception as user_error:
                error(f"Error creating session for identifier '{identifier}': {str(user_error)}")
                failed_sessions.append(f"{identifier} (error: {str(user_error)})")

        # Prepare and return the final response
        if not created_sessions and failed_sessions:
            raise_http_exception(
                status_code=500, detail=f"Failed to create any sessions. Errors: {', '.join(failed_sessions)}"
            )

        response_data = {"sessions": created_sessions}
        
        # Add warnings for failed sessions
        warnings = []
        if failed_sessions:
            warnings.append(f"Failed to create sessions for: {', '.join(failed_sessions)}")
        
        # Add email sending results
        emails_sent = sum(1 for result in email_results if result["sent"])
        emails_skipped = sum(1 for result in email_results if result.get("skipped", False))
        emails_failed = len(email_results) - emails_sent - emails_skipped

        if email_results:
            if emails_sent > 0:
                info(f"Email invitations sent successfully to {emails_sent} users")
            if emails_skipped > 0:
                info(f"Skipped sending emails to {emails_skipped} users (improwised.com domain)")
            if emails_failed > 0:
                warnings.append(f"Failed to send email invitations to {emails_failed} users")
        elif email_service.is_configured:
            warnings.append("No email addresses available for sending invitations")
        else:
            info("Email service not configured - no invitations sent")
        
        if warnings:
            response_data["warnings"] = "; ".join(warnings)

        # Include email results in response for debugging
        if email_results:
            response_data["email_notifications"] = {
                "sent": emails_sent,
                "skipped": emails_skipped,
                "failed": emails_failed,
                "details": email_results
            }

        hashed_data = hash_ids_in_response(response_data)

        success_message = f"Generated {len(created_sessions)} session codes successfully."
        if emails_sent > 0:
            success_message += f" Email invitations sent to {emails_sent} users."
        if emails_skipped > 0:
            success_message += f" Skipped {emails_skipped} emails (improwised.com domain)."

        return success_response(
            data=hashed_data,
            message=success_message,
        )

    except HTTPException as http_exc:
        raise http_exc
    except Exception as e:
        error(f"Unexpected error in generate_sessions: {str(e)}", exc_info=True)
        raise_http_exception(status_code=500, detail="An unexpected error occurred while generating sessions.")


# =============================================================================
# VALIDATE SESSION API ENDPOINT (REFACTORED)
# =============================================================================


@sessions_router.post("/validate_session_code")
def validate_session_code(request: SessionCodeRequest):
    session_code_input = request.session_code
    if not session_code_input:
        raise_http_exception(status_code=400, detail="Session code is required.")

    try:
        session_code = validate_session_code_format(session_code_input)

        session_details = get_session_for_start_or_validation(session_code)

        if not session_details:
            raise_http_exception(status_code=404, detail="Invalid or expired session code.")

        # All details, including question_selection_mode, are now in one object
        response_data = {
            "session_id": session_details["id"],
            "session_code": request.session_code,
            "assessment_id": session_details["assessment_id"],
            "assessment_name": session_details["assessment_name"],
            "is_final": session_details["is_final"],
            "username": session_details.get("username", ""),
            "session_status": session_details["session_status"],
            # You may need to add remaining_time_seconds logic back here or in the data function
            "remaining_time_seconds": session_details.get("remaining_time_seconds", 0),
            "started_at": session_details["started_at"].isoformat() if session_details["started_at"] else None,
            "completed_at": session_details["completed_at"].isoformat() if session_details["completed_at"] else None,
            "question_selection_mode": session_details["question_selection_mode"],
        }
        hashed_data = hash_ids_in_response(response_data)
        return success_response(data=hashed_data, message="Session code validated successfully")

    except HTTPException:
        raise
    except Exception as e:
        error(f"Error validating session code {session_code}: {str(e)}", exc_info=True)
        raise_http_exception(status_code=500, detail=f"Server error: {str(e)}")


# =============================================================================
# START SESSION API ENDPOINT
# =============================================================================
@sessions_router.post("/start_session")
def start_session(request: StartSessionRequest):
    session_code_input = request.session_code
    if not session_code_input:
        raise_http_exception(status_code=400, detail="Session code is required.")

    try:
        session_code = validate_session_code_format(session_code_input)
        # 1. Get session details to validate
        session_details = get_session_for_start_or_validation(session_code)

        if not session_details:
            raise_http_exception(status_code=404, detail="Invalid or expired session code.")

        if session_details["session_status"] == "completed":
            raise_http_exception(
                status_code=409,
                detail=f"Session cannot be started. Current status: {session_details['session_status']}",
            )
        elif session_details["session_status"] == "in_progress":
            # Session is already in progress, this is okay - just return success
            info(f"Session {session_code} is already in progress, returning current state")
            response_data = {
                "session_id": session_details["id"],
                "session_code": session_code,
                "assessment_id": session_details["assessment_id"],
                "assessment_name": session_details["assessment_name"],
                "is_final": session_details["is_final"],
                "username": session_details.get("username", ""),
                "session_status": session_details["session_status"],
                "started_at": (session_details["started_at"].isoformat() if session_details["started_at"] else None),
                "completed_at": (
                    session_details["completed_at"].isoformat() if session_details["completed_at"] else None
                ),
            }
            hashed_data = hash_ids_in_response(response_data)
            return success_response(data=hashed_data, message="Session already in progress")
        elif session_details["session_status"] != "pending":
            raise_http_exception(
                status_code=409,
                detail=f"Session cannot be started. Current status: {session_details['session_status']}",
            )

        # 2. Update the session in the database
        rows_updated = start_session_in_db(session_details["id"])
        if rows_updated == 0:
            raise_http_exception(status_code=500, detail="Failed to start session, status might have changed.")

        # 3. Refresh session details to get updated timestamps
        updated_session_details = get_session_for_start_or_validation(session_code)

        # Format and return the response
        response_data = {
            "session_id": updated_session_details["id"],
            "session_code": session_code,
            "assessment_id": updated_session_details["assessment_id"],
            "assessment_name": updated_session_details["assessment_name"],
            "is_final": updated_session_details["is_final"],
            "username": updated_session_details.get("username", ""),
            "session_status": updated_session_details["session_status"],
            "started_at": (
                updated_session_details["started_at"].isoformat() if updated_session_details["started_at"] else None
            ),
            "completed_at": (
                updated_session_details["completed_at"].isoformat() if updated_session_details["completed_at"] else None
            ),
        }
        hashed_data = hash_ids_in_response(response_data)
        return success_response(data=hashed_data, message="Session started successfully")

    except HTTPException:
        raise
    except Exception as e:
        error(f"Error starting session {session_code_input}: {str(e)}", exc_info=True)
        raise_http_exception(status_code=500, detail=f"Server error: {str(e)}")


# =============================================================================
# RESULT ON SESSION API ENDPOINT
# =============================================================================
@sessions_router.get("/admin/sessions/{session_id}/results")
async def get_session_results(session_id: str):
    try:
        # 1. Get the completed session details
        # Note: Assumes session_id can be a code or numeric ID. Hashing would be handled here if needed.
        session_dict = get_completed_session_for_results(session_id)

        if not session_dict:
            return error_response(
                message="Completed session not found",
                code=status.HTTP_404_NOT_FOUND,
            )

        # 2. Get the detailed answers for that session
        answered_questions_raw = get_session_answers_for_results(session_dict["id"])

        # The rest of the logic is for processing and formatting, which is fine to keep here.
        answered_questions, correct_answers, total_score = [], 0, 0
        for answer in answered_questions_raw:
            answer_dict = dict(answer)

            # Parse options safely
            options = safe_json_loads(answer_dict["options"], {})

            question_data = {
                "question": answer_dict["question"],
                "options": options,
                "userAnswer": answer_dict["user_answer"],
                "correctAnswerKey": answer_dict["correct_answer_key"],
                "isCorrect": answer_dict["is_correct"],
                "score": float(answer_dict["score"]) if answer_dict["score"] else 0,
                "level": answer_dict["level"],
            }

            answered_questions.append(question_data)

            if answer_dict["is_correct"]:
                correct_answers += 1

            total_score += question_data["score"]

        # Prepare response data (simplified for brevity)
        result_data = {
            "session_info": {
                "session_code": session_dict["session_code"],
                "username": session_dict["username"],
                "assessment_name": session_dict["assessment_name"],
                "completed_at": (session_dict["completed_at"].isoformat() if session_dict["completed_at"] else None),
                "question_selection_mode": session_dict["question_selection_mode"],
            },
            "score_summary": {
                "correct_answers": correct_answers,
                "total_questions": session_dict["total_questions"],
                "questions_attempted": len(answered_questions),
                "calculated_score": round(total_score, 2),
                "final_score": (
                    float(session_dict["score"]) if session_dict["score"] is not None else round(total_score, 2)
                ),
            },
            "answered_questions": answered_questions,
        }
        return success_response(data=result_data, message="Session results retrieved successfully")

    except Exception as e:
        error(f"Error getting session results for ID {session_id}: {str(e)}")
        return error_response(message=f"Error getting session results: {str(e)}", code=500)


# =============================================================================
# USER ON SESSION API ENDPOINT
# =============================================================================
@sessions_router.get("/admin/sessions/{session_code}/user")
async def get_session_user(session_code: str):
    try:
        if not session_code or len(session_code) != 6 or not session_code.isdigit():
            raise_http_exception(status_code=400, detail="Session code must be a 6-digit number")

        details = get_session_user_details(session_code)

        if not details:
            raise_http_exception(status_code=404, detail="Session code not found")

        username = details["display_name"] or details["external_id"]

        response_data = {
            "username": username,
            "assessment_id": details["assessment_id"],
            "assessment_name": details["assessment_name"],
            "is_final": details["is_final"],
            # Fixed: use correct key and provide default
            "session_status": details.get("session_status", "pending"),
        }
        return hash_ids_in_response(response_data)

    except HTTPException:
        raise
    except Exception as e:
        error(f"Error fetching session user for code {session_code}: {str(e)}")
        raise_http_exception(status_code=500, detail=f"Error fetching session user: {str(e)}")


# =============================================================================
# SESSION ON USER EMAIL API ENDPOINT
# =============================================================================


@sessions_router.get("/user/{email}/sessions")
async def get_user_sessions_by_email(email: str, current_user: dict = Depends(get_current_user)):
    """Get all sessions for a specific user identified by email."""
    try:
        # Verify that the requesting user can only access their own sessions
        user_email = current_user.get("email")
        if not user_email:
            raise_http_exception(status_code=401, detail="User email not found in authentication data")

        # Normalize emails for comparison (case-insensitive)
        if user_email.lower() != email.lower():
            warning(f"User {user_email} attempted to access sessions for {email}")
            raise_http_exception(status_code=403, detail="Access denied. You can only view your own sessions.")

        # 1. Get the user's internal ID using the new data access function
        user_id = get_user_id_by_email(email)

        if not user_id:
            warning(f"User with email {email} not found in database")
            # Return successfully with an empty list, matching original behavior
            return success_response(
                data={"sessions": [], "total": 0},
                message=f"No user found with email {email}",
            )

        # 2. Get all sessions for this user using the other new function
        sessions = get_sessions_by_user_id(user_id)

        # 3. Post-processing and response formatting (outside the DB connection block)
        hashed_sessions = hash_ids_in_response(sessions)

        return success_response(
            data={
                "sessions": hashed_sessions,
                "total": len(sessions),
            },
            message="User sessions retrieved successfully",
        )

    except HTTPException:
        raise
    except Exception as e:
        error(f"Error getting sessions for user {email}: {str(e)}", exc_info=True)
        # Use a more generic error message for the user
        raise_http_exception(status_code=500, detail="An unexpected error occurred while retrieving user sessions.")


# =============================================================================
# SESSION ON USER EMAIL API ENDPOINT
# =============================================================================


@sessions_router.post("/admin/generate-link")
async def generate_quiz_link(request_data: dict, _: None = Depends(rate_limiter)):
    """Generate a shareable link for users to take an assessment"""
    try:
        assessment_id = request_data.get("assessment_id")

        if not assessment_id:
            raise_http_exception(status_code=400, detail="Assessment ID is required")

        assessment = validate_assessment_exists(assessment_id)

        if not assessment:
            raise_http_exception(status_code=404, detail=f"Assessment with ID {assessment_id} not found")

        frontend_url = os.getenv("FRONTEND_URL")

        # Generate a unique 6-digit session code
        session_code = str(random.randint(100000, 999999)).zfill(6)
        hashed_session_code = encode_session_code(session_code)
        quiz_link = f"{frontend_url}/quiz/{hashed_session_code}"

        response_data = {
            "link": quiz_link,
            "assessment_id": assessment_id,
            "assessment_name": assessment["name"],
            "session_code": session_code,
            "hashed_session_code": hashed_session_code,
        }
        hashed_data = hash_ids_in_response(response_data)
        return success_response(data=hashed_data, message="Quiz link generated successfully")

    except HTTPException:
        raise
    except Exception as e:
        error(f"Error generating quiz link: {str(e)}")
        raise_http_exception(status_code=500, detail=f"Error generating quiz link: {str(e)}")
