#!/bin/bash

# HERBIT Environment Setup Script
# This script ensures all environment variables are properly configured

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if environment files exist
check_env_files() {
    print_status "Checking environment files..."
    
    if [ ! -f "backend/.env" ]; then
        print_warning "backend/.env not found, copying from example..."
        cp backend/.env_example backend/.env
        print_success "Created backend/.env from example"
    else
        print_success "backend/.env exists"
    fi
    
    if [ ! -f "frontend/.env" ]; then
        print_warning "frontend/.env not found, copying from example..."
        cp frontend/.env_example frontend/.env
        print_success "Created frontend/.env from example"
    else
        print_success "frontend/.env exists"
    fi
}

# Set required environment variables for docker-compose
setup_docker_env() {
    print_status "Setting up Docker environment variables..."
    
    # Export variables needed by docker-compose.yaml
    export PG_DATABASE=herbit
    export PG_USER=herbit
    export PG_PASSWORD=herbit
    export TAG=latest
    
    # Create .env file in root for docker-compose
    cat > .env << EOF
# Docker Compose Environment Variables
PG_DATABASE=herbit
PG_USER=herbit
PG_PASSWORD=herbit
TAG=latest
EOF
    
    print_success "Docker environment variables configured"
}

# Validate configuration
validate_config() {
    print_status "Validating configuration..."
    
    # Check if API key is set in backend/.env
    if grep -q "<your_api_key>" backend/.env 2>/dev/null; then
        print_warning "API key not configured in backend/.env"
        print_warning "Please edit backend/.env and replace <your_api_key> with your actual API key"
        print_warning "You can get an API key from: https://llmui.pride.improwised.dev/"
    else
        print_success "API key appears to be configured"
    fi
    
    print_success "Configuration validation completed"
}

# Main function
main() {
    echo "🔧 HERBIT Environment Setup"
    echo "=========================="
    
    check_env_files
    setup_docker_env
    validate_config
    
    echo ""
    print_success "Environment setup completed!"
    echo ""
    echo "Next steps:"
    echo "1. Run: ./docker-up.sh up"
    echo "2. Wait for services to start"
    echo "3. Access: http://localhost:5173"
    echo ""
    echo "If you need to configure the API key:"
    echo "1. Edit backend/.env"
    echo "2. Replace the API_KEY value with your key from llmui.pride.improwised.dev"
    echo "3. Restart with: ./docker-up.sh restart"
}

# Run main function
main "$@"
